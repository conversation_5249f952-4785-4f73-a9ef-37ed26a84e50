import { useState } from "react"
import { Link } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, Heart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface Vehicle {
  id: number;
  model: string;
  slug: string;
  price: number;
  promotional_price?: number;
  year_manufacture: number;
  mileage: number;
  color: string;
  fuel_type: string;
  transmission: string;
  is_featured: boolean;
  is_negotiable: boolean;
  main_image_url?: string;
  url: string;
  brand: {
    name: string;
  };
  category: {
    name: string;
    slug: string;
  };
}

interface Part {
  id: number;
  name: string;
  slug: string;
  price: number;
  promotional_price?: number;
  stock_quantity: number;
  is_original: boolean;
  is_featured: boolean;
  main_image_url?: string;
  url: string;
  brand: {
    name: string;
  };
}

interface FeaturedListingsProps {
  vehicles?: Vehicle[];
  parts?: Part[];
  title?: string;
  showMoreLink?: string;
}

export default function FeaturedListings({ 
  vehicles = [], 
  parts = [], 
  title = "Destaques",
  showMoreLink 
}: FeaturedListingsProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [favorites, setFavorites] = useState<Set<string>>(new Set())

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getFinalPrice = (item: Vehicle | Part) => {
    return item.promotional_price || item.price;
  };

  const getMainImage = (item: Vehicle | Part) => {
    if (item.main_image_url) {
      return item.main_image_url;
    }
    
    // Imagens placeholder baseadas no tipo
    if ('model' in item) {
      // É um veículo
      const vehicleImages = [
        '/toyota-corolla-2023-white-car.jpg',
        '/honda-civic-2022-silver-car.jpg',
        '/placeholder.jpg'
      ];
      return vehicleImages[Math.floor(Math.random() * vehicleImages.length)];
    } else {
      // É uma peça
      return '/car-suspension-kit-parts.jpg';
    }
  };

  const allItems = [
    ...vehicles.map(v => ({ ...v, type: 'vehicle' as const })),
    ...parts.map(p => ({ ...p, type: 'part' as const }))
  ];

  const itemsPerView = 4
  const maxIndex = Math.max(0, allItems.length - itemsPerView)

  const nextSlide = () => {
    setCurrentIndex((prev) => Math.min(prev + 1, maxIndex))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => Math.max(prev - 1, 0))
  }

  const toggleFavorite = (itemId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev)
      if (newFavorites.has(itemId)) {
        newFavorites.delete(itemId)
      } else {
        newFavorites.add(itemId)
      }
      return newFavorites
    })
  }

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-foreground">{title}</h2>
          {showMoreLink && (
            <Link 
              href={showMoreLink}
              className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center"
            >
              Ver todos
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          )}
        </div>

        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg border-gray-200 hover:bg-gray-50"
            onClick={prevSlide}
            disabled={currentIndex === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg border-gray-200 hover:bg-gray-50"
            onClick={nextSlide}
            disabled={currentIndex >= maxIndex}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Items Container */}
          <div className="overflow-hidden mx-12">
            <div
              className="flex transition-transform duration-300 ease-in-out gap-4"
              style={{ transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)` }}
            >
              {allItems.map((item) => (
                <div key={`${item.type}-${item.id}`} className="flex-none w-1/4">
                  <Card className="group cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200">
                    <div className="relative">
                      {/* Item Image */}
                      <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
                        <img
                          src={getMainImage(item)}
                          alt={'model' in item ? item.model : item.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      </div>

                      {/* Favorite Button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2 bg-white/80 hover:bg-white shadow-sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleFavorite(`${item.type}-${item.id}`)
                        }}
                      >
                        <Heart
                          className={cn(
                            "h-4 w-4",
                            favorites.has(`${item.type}-${item.id}`) ? "fill-red-500 text-red-500" : "text-gray-600",
                          )}
                        />
                      </Button>

                      {/* Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-2">
                        {item.is_featured && (
                          <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white">
                            ⭐ Destaque
                          </Badge>
                        )}
                        {item.promotional_price && (
                          <Badge className="bg-green-500 hover:bg-green-600 text-white">
                            🏷️ Oferta
                          </Badge>
                        )}
                        {'is_original' in item && item.is_original && (
                          <Badge className="bg-blue-500 hover:bg-blue-600 text-white">
                            ✅ Original
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Item Info */}
                    <CardContent className="p-4">
                      <h3 className="font-medium text-foreground text-sm mb-2 line-clamp-2">
                        {'model' in item ? `${item.brand.name} ${item.model}` : item.name}
                      </h3>

                      {/* Price */}
                      <div className="mb-2">
                        <span className="text-lg font-semibold text-foreground">
                          {formatPrice(getFinalPrice(item))}
                        </span>
                        {item.promotional_price && (
                          <span className="text-sm text-muted-foreground line-through ml-2">
                            {formatPrice(item.price)}
                          </span>
                        )}
                      </div>

                      {/* Details specific to type */}
                      {'model' in item ? (
                        // Vehicle
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div className="flex justify-between">
                            <span>Ano:</span>
                            <span className="font-medium">{item.year_manufacture}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Km:</span>
                            <span className="font-medium">{item.mileage.toLocaleString('pt-BR')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Combustível:</span>
                            <span className="font-medium">{item.fuel_type}</span>
                          </div>
                          {item.is_negotiable && (
                            <div className="text-blue-600 font-medium text-xs">
                              💰 Preço negociável
                            </div>
                          )}
                        </div>
                      ) : (
                        // Part
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div className="flex justify-between">
                            <span>Marca:</span>
                            <span className="font-medium">{item.brand.name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Estoque:</span>
                            <span className={`font-medium ${item.stock_quantity > 10 ? 'text-green-600' : 'text-orange-600'}`}>
                              {item.stock_quantity} unidades
                            </span>
                          </div>
                          <div className="text-xs">
                            {item.is_original ? 'Peça original' : 'Peça compatível'}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>

        {allItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📦</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhum item em destaque no momento
            </h3>
            <p className="text-gray-600">
              Volte em breve para ver novas ofertas e produtos em destaque.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
