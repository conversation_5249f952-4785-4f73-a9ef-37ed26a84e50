import FeaturedListings from '@/components/FeaturedListings';
import HeroCarousel from '@/components/HeroCarousel';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface Vehicle {
    id: number;
    model: string;
    slug: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_featured: boolean;
    is_negotiable: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
    category: {
        name: string;
        slug: string;
    };
}

interface Part {
    id: number;
    name: string;
    slug: string;
    price: number;
    promotional_price?: number;
    stock_quantity: number;
    is_original: boolean;
    is_featured: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
}

interface WelcomeProps extends PageProps {
    categories: Category[];
    featuredVehicles?: Vehicle[];
    featuredParts?: Part[];
}

export default function Welcome({
    categories = [],
    featuredVehicles = [],
    featuredParts = [],
}: WelcomeProps) {
    return (
        <MainLayout categories={categories}>
            <div className="space-y-0">
                <HeroCarousel />
                
                <div className="container mx-auto px-4 py-12">
                    <FeaturedListings
                        vehicles={featuredVehicles}
                        parts={featuredParts}
                        title="Ofertas em Destaque"
                        showMoreLink="/anuncios"
                    />
                </div>
            </div>
        </MainLayout>
    );
}
