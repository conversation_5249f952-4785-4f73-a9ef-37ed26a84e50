import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

// Dados dos slides (pode ser movido para um arquivo separado ou vindo de uma API)
const slides = [
  {
    id: 1,
    title: 'Encontre o veículo perfeito para você',
    description: 'Mais de 10.000 veículos novos e seminovos em todo o Brasil.',
    buttonText: 'Ver ofertas',
    buttonLink: '/anuncios',
    badge: 'A plataforma mais confiável do Brasil',
    image: 'https://images.unsplash.com/photo-1493238792000-8113da705763?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    id: 2,
    title: 'As melhores ofertas em peças automotivas',
    description: 'Peças originais e compatíveis com os melhores preços do mercado.',
    buttonText: 'Comprar agora',
    buttonLink: '/pecas',
    badge: 'Frete grátis acima de R$ 200',
    image: 'https://images.unsplash.com/photo-1605894331529-78ccd56530ed?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    id: 3,
    title: 'Financiamento facilitado',
    description: 'Taxas especiais e aprovação rápida para você realizar o sonho do seu carro.',
    buttonText: 'Simular financiamento',
    buttonLink: '/financiamento',
    badge: 'Parcele em até 60x',
    image: 'https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  }
];

export default function HeroCarousel() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <section className="relative bg-white text-gray-900 overflow-hidden">
      <Swiper
        spaceBetween={0}
        centeredSlides={true}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
        }}
        navigation={!isMobile}
        modules={[Autoplay, Pagination, Navigation]}
        className="w-full h-full"
      >
        {slides.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div className="relative h-[500px] md:h-[600px] lg:h-[700px] w-full">
              {/* Overlay gradiente */}
              <div className="absolute inset-0 bg-gradient-to-r from-gray-900/80 to-gray-900/30 z-10"></div>
              
              {/* Imagem de fundo */}
              <div 
                className="absolute inset-0 bg-cover bg-center transition-transform duration-1000 ease-in-out"
                style={{ backgroundImage: `url(${slide.image})` }}
              ></div>
              
              {/* Conteúdo do slide */}
              <div className="relative z-20 h-full flex items-center">
                <div className="container mx-auto px-4 md:px-6 lg:px-8">
                  <div className="max-w-2xl">
                    {/* Badge */}
                    <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm text-white text-sm font-medium mb-6">
                      <span className="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                      {slide.badge}
                    </div>
                    
                    {/* Título e descrição */}
                    <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-4">
                      {slide.title}
                    </h1>
                    <p className="text-lg md:text-xl text-gray-200 mb-8 max-w-lg">
                      {slide.description}
                    </p>
                    
                    {/* Botão CTA */}
                    <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold text-base md:text-lg px-8 py-4">
                      <Link href={slide.buttonLink}>
                        {slide.buttonText}
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      
      {/* Onda decorativa na parte inferior */}
      <div className="absolute bottom-0 left-0 right-0 z-30">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0V120Z" fill="white"/>
        </svg>
      </div>
    </section>
  );
}
